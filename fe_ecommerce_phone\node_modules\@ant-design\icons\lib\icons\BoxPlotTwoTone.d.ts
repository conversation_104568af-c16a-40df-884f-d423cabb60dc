import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![box-plot](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI5NiAzNjhoODh2Mjg4aC04OHptMTUyIDBoMjgwdjI4OEg0NDh6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik05NTIgMjI0aC01MmMtNC40IDAtOCAzLjYtOCA4djI0OGgtOTJWMzA0YzAtNC40LTMuNi04LTgtOEgyMzJjLTQuNCAwLTggMy42LTggOHYxNzZoLTkyVjIzMmMwLTQuNC0zLjYtOC04LThINzJjLTQuNCAwLTggMy42LTggOHY1NjBjMCA0LjQgMy42IDggOCA4aDUyYzQuNCAwIDgtMy42IDgtOFY1NDhoOTJ2MTcyYzAgNC40IDMuNiA4IDggOGg1NjBjNC40IDAgOC0zLjYgOC04VjU0OGg5MnYyNDRjMCA0LjQgMy42IDggOCA4aDUyYzQuNCAwIDgtMy42IDgtOFYyMzJjMC00LjQtMy42LTgtOC04ek0zODQgNjU2aC04OFYzNjhoODh2Mjg4em0zNDQgMEg0NDhWMzY4aDI4MHYyODh6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
