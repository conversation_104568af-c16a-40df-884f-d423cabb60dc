import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![box-plot](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk1MiAyMjRoLTUyYy00LjQgMC04IDMuNi04IDh2MjQ4aC05MlYzMDRjMC00LjQtMy42LTgtOC04SDIzMmMtNC40IDAtOCAzLjYtOCA4djE3NmgtOTJWMjMyYzAtNC40LTMuNi04LTgtOEg3MmMtNC40IDAtOCAzLjYtOCA4djU2MGMwIDQuNCAzLjYgOCA4IDhoNTJjNC40IDAgOC0zLjYgOC04VjU0OGg5MnYxNzJjMCA0LjQgMy42IDggOCA4aDU2MGM0LjQgMCA4LTMuNiA4LThWNTQ4aDkydjI0NGMwIDQuNCAzLjYgOCA4IDhoNTJjNC40IDAgOC0zLjYgOC04VjIzMmMwLTQuNC0zLjYtOC04LTh6TTI5NiAzNjhoODh2Mjg4aC04OFYzNjh6bTQzMiAyODhINDQ4VjM2OGgyODB2Mjg4eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
